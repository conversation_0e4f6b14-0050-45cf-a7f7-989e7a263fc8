import 'package:collection/collection.dart';
import 'package:duplo/duplo.dart';
import 'package:e_trader/src/assets/assets.gen.dart' as trader;
import 'package:e_trader/src/data/api/news_response_model.dart';
import 'package:e_trader/src/domain/formatter/date_formatter.dart';
import 'package:e_trader/src/presentation/discover/news/bloc/news_bloc.dart';
import 'package:e_trader/src/presentation/discover/news/widgets/events_news_loading_view.dart';
import 'package:e_trader/src/presentation/discover/news/widgets/news_list_item.dart';
import 'package:e_trader/src/presentation/duplo/sticky_delegate.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart' as intl;

class NewsTab extends StatefulWidget {
  const NewsTab({this.isInMarketDetails = false});
  final bool isInMarketDetails;

  @override
  State<NewsTab> createState() => _NewsTabState();
}

class _NewsTabState extends State<NewsTab> with AutomaticKeepAliveClientMixin {
  late final TextEditingController _textController;
  late final ScrollController _scrollController;

  @override
  void initState() {
    super.initState();
    _textController = TextEditingController();
    _scrollController = ScrollController();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _textController.dispose();
    super.dispose();
  }

  void _onScroll() {
    final newsBloc = context.read<NewsBloc>();
    final state = newsBloc.state;

    // Find the first visible date header
    final visibleDateLabel = _findVisibleDateLabel(state.groupedNews);
    if (visibleDateLabel != null && visibleDateLabel != state.dateLabel) {
      print(
        'Updating date label from "${state.dateLabel}" to "$visibleDateLabel"',
      );
      newsBloc.add(NewsEvent.updateDateLabel(dateLabel: visibleDateLabel));
    }
  }

  void _initializeDateLabel() {
    final newsBloc = context.read<NewsBloc>();
    final state = newsBloc.state;

    if (state.groupedNews.isNotEmpty && state.dateLabel.isEmpty) {
      // Find the first date header in the list
      final firstDateLabel = _findFirstDateLabel(state.groupedNews);
      if (firstDateLabel != null) {
        newsBloc.add(NewsEvent.updateDateLabel(dateLabel: firstDateLabel));
      }
    }
  }

  String? _findFirstDateLabel(List<Object?> groupedNews) {
    for (final item in groupedNews) {
      if (item is DayPart) {
        final l10n = EquitiLocalization.of(context);
        return DateFormatter.localizedDay(item, l10n);
      } else if (item is DateTime) {
        return intl.DateFormat('MMM d, yyyy').format(item);
      }
    }
    return null;
  }

  String? _findVisibleDateLabel(List<Object?> groupedNews) {
    if (groupedNews.isEmpty) return null;

    // Get the current scroll position
    final scrollOffset = _scrollController.offset;
    final maxScrollExtent = _scrollController.position.maxScrollExtent;

    // If we're at the top, return the first date label
    if (scrollOffset <= 0) {
      return _findFirstDateLabel(groupedNews);
    }

    // If we're at the bottom, return the last date label
    if (scrollOffset >= maxScrollExtent) {
      return _findLastDateLabel(groupedNews);
    }

    // Calculate a rough estimate of which section we're in
    // This is a simplified approach - in a real app you might want to use
    // a more sophisticated method like tracking item positions
    final scrollProgress = scrollOffset / maxScrollExtent;
    final estimatedIndex = (scrollProgress * groupedNews.length).floor().clamp(
      0,
      groupedNews.length - 1,
    );

    print(
      'Scroll offset: $scrollOffset, Progress: $scrollProgress, Estimated index: $estimatedIndex',
    );

    // Look backwards from estimated index to find the most recent date header
    for (int i = estimatedIndex; i >= 0; i--) {
      final item = groupedNews.elementAtOrNull(i);
      print('Checking item at index $i: ${item.runtimeType}');
      if (item is DayPart) {
        final l10n = EquitiLocalization.of(context);
        final label = DateFormatter.localizedDay(item, l10n);
        print('Found DayPart: $item -> $label');
        return label;
      } else if (item is DateTime) {
        final label = intl.DateFormat('MMM d, yyyy').format(item);
        print('Found DateTime: $item -> $label');
        return label;
      }
    }

    return null;
  }

  String? _findLastDateLabel(List<Object?> groupedNews) {
    for (int i = groupedNews.length - 1; i >= 0; i--) {
      final item = groupedNews.elementAtOrNull(i);
      if (item is DayPart) {
        final l10n = EquitiLocalization.of(context);
        return DateFormatter.localizedDay(item, l10n);
      } else if (item is DateTime) {
        return intl.DateFormat('MMM d, yyyy').format(item);
      }
    }
    return null;
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final theme = context.duploTheme;
    final l10n = EquitiLocalization.of(context);
    final locale = Localizations.localeOf(context);

    return CustomScrollView(
      controller: _scrollController,
      slivers: [
        BlocBuilder<NewsBloc, NewsState>(
          buildWhen: (previous, current) => previous != current,
          builder: (blocBuilderContext, state) {
            // Initialize date label when news data is first loaded
            WidgetsBinding.instance.addPostFrameCallback((_) {
              _initializeDateLabel();
            });

            return switch (state.processState) {
              NewsLoading() => SliverFillRemaining(
                hasScrollBody: false,
                child: EventsNewsLoadingView(),
              ),
              NewsError() => SliverFillRemaining(
                hasScrollBody: false,
                child: EmptyOrErrorStateComponent.defaultError(
                  blocBuilderContext,
                  () {
                    blocBuilderContext.read<NewsBloc>().add(
                      NewsEvent.fetchNews(),
                    );
                  },
                ),
              ),
              _ => SliverMainAxisGroup(
                slivers: [
                  // if (!widget.isInMarketDetails)
                  //   SliverToBoxAdapter(
                  //     child: ColoredBox(
                  //       color: theme.background.bgSecondary,
                  //       child: Padding(
                  //         padding: const EdgeInsets.all(16),
                  //         child: DuploSearchInputField(
                  //           key: Key('symbols_search'),
                  //           onChanged: (_) {
                  //             debugPrint('onTextChanged');
                  //           },
                  //           controller: _textController,
                  //           hintText: l10n.trader_search,
                  //           isDisabled: true,
                  //           onTap:
                  //               () => showNewsSearchView(
                  //                 parentContext: context,
                  //                 textController: _textController,
                  //                 title: l10n.trader_search,
                  //               ),
                  //         ),
                  //       ),
                  //     ),
                  //   ),
                  SliverPersistentHeader(
                    pinned: true,
                    delegate: StickyDelegate(
                      child: Container(
                        color: theme.background.bgSecondary,
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 8,
                        ),
                        alignment:
                            intl.Bidi.isRtlLanguage(locale.languageCode)
                                ? Alignment.centerRight
                                : Alignment.centerLeft,
                        width: double.infinity,
                        child: DuploText(
                          textAlign: TextAlign.start,
                          text: state.dateLabel,
                          style: context.duploTextStyles.textMd,
                          fontWeight: DuploFontWeight.semiBold,
                          color: theme.text.textPrimary,
                        ),
                      ),
                    ),
                  ),

                  // News content
                  SliverPadding(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    sliver: PagedView.sliver(
                      padding: EdgeInsets.zero,
                      itemCount: state.groupedNews.length,
                      centerError: true,
                      centerLoading: true,
                      centerEmpty: true,
                      isLoading: switch (state.processState) {
                        NewsLoading() => true,
                        _ => false,
                      },
                      loadingBuilder:
                          (ctx) => Center(
                            child:
                                _textController.text.length < 1
                                    ? const SizedBox()
                                    : EventsNewsLoadingView(),
                          ),
                      emptyBuilder:
                          (ctx) => EmptyOrErrorStateComponent.empty(
                            title: l10n.trader_nothingToShow,
                            description: l10n.trader_noResultsDescription,
                            svgImage: trader.Assets.images.emptySearch.svg(
                              allowDrawingOutsideViewBox: true,
                            ),
                          ),
                      errorBuilder:
                          (ctx) => EmptyOrErrorStateComponent.error(
                            description:
                                l10n.trader_somethingWentWrongDescription,
                            title: l10n.trader_somethingWentWrong,
                            svgImage: trader.Assets.images.bug.svg(),
                            retryButtonText: l10n.trader_reload,
                            onTapRetry:
                                () => blocBuilderContext.read<NewsBloc>().add(
                                  NewsEvent.fetchNews(),
                                ),
                          ),
                      hasReachedMax: state.hasReachedMax,
                      separatorBuilder: (ctx, index) => SizedBox(height: 16),
                      onFetchData: () {
                        if (!state.hasReachedMax &&
                            state.processState !=
                                const NewsProcessState.loadingMore()) {
                          blocBuilderContext.read<NewsBloc>().add(
                            NewsEvent.fetchNews(loadMore: true),
                          );
                        }
                      },
                      itemBuilder: (BuildContext ctx, int index) {
                        final item = state.groupedNews[index];
                        if (item is DayPart || item is DateTime) {
                          // Date headers are invisible but still take up space in the list
                          // The actual header display is handled by the sticky header above
                          return const SizedBox.shrink();
                        } else if (item is NewsItem) {
                          return NewsListItem(newsItemDetails: item.item);
                        }
                        return const SizedBox.shrink();
                      },
                    ),
                  ),
                  !state.hasReachedMax
                      ? SliverPersistentHeader(
                        pinned: true,
                        floating: true,
                        delegate: StickyDelegate(
                          child: Center(
                            child: CircularProgressIndicator.adaptive(),
                          ),
                        ),
                      )
                      : SliverToBoxAdapter(),
                ],
              ),
            };
          },
        ),
      ],
    );
  }

  @override
  bool get wantKeepAlive => true;
}

import 'package:collection/collection.dart';
import 'package:duplo/duplo.dart';
import 'package:e_trader/src/assets/assets.gen.dart' as trader;
import 'package:e_trader/src/data/api/news_response_model.dart';
import 'package:e_trader/src/domain/formatter/date_formatter.dart';
import 'package:e_trader/src/presentation/discover/news/bloc/news_bloc.dart';
import 'package:e_trader/src/presentation/discover/news/widgets/events_news_loading_view.dart';
import 'package:e_trader/src/presentation/discover/news/widgets/news_list_item.dart';
import 'package:e_trader/src/presentation/duplo/sticky_delegate.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart' as intl;

class NewsTab extends StatefulWidget {
  const NewsTab({this.isInMarketDetails = false});
  final bool isInMarketDetails;

  @override
  State<NewsTab> createState() => _NewsTabState();
}

class _NewsTabState extends State<NewsTab> with AutomaticKeepAliveClientMixin {
  late final TextEditingController _textController;
  late final ScrollController _scrollController;
  String? _lastVisibleDateLabel;
  final Map<int, GlobalKey> _itemKeys = {};
  final Map<int, String> _itemDateLabels = {};
  String? _currentDateLabelForItems;

  @override
  void initState() {
    super.initState();
    _textController = TextEditingController();
    _scrollController = ScrollController();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _textController.dispose();
    super.dispose();
  }

  void _onScroll() {
    final newsBloc = context.read<NewsBloc>();
    final state = newsBloc.state;

    // Find the date header that should be visible at the top
    final visibleDateLabel = _findTopVisibleItemDateLabel();

    // Only update if the label has actually changed and is different from current state
    if (visibleDateLabel != null &&
        visibleDateLabel != state.dateLabel &&
        visibleDateLabel != _lastVisibleDateLabel) {
      _lastVisibleDateLabel = visibleDateLabel;
      print('Updating date label to "$visibleDateLabel"');
      newsBloc.add(NewsEvent.updateDateLabel(dateLabel: visibleDateLabel));
    }
  }

  void _initializeDateLabel() {
    final newsBloc = context.read<NewsBloc>();
    final state = newsBloc.state;

    if (state.groupedNews.isNotEmpty && state.dateLabel.isEmpty) {
      // Find the first date header in the list
      final firstDateLabel = _findFirstDateLabel(state.groupedNews);
      if (firstDateLabel != null) {
        newsBloc.add(NewsEvent.updateDateLabel(dateLabel: firstDateLabel));
      }
    }
  }

  String? _findFirstDateLabel(List<Object?> groupedNews) {
    for (final item in groupedNews) {
      if (item is DayPart) {
        final l10n = EquitiLocalization.of(context);
        return DateFormatter.localizedDay(item, l10n);
      } else if (item is DateTime) {
        return intl.DateFormat('MMM d, yyyy').format(item);
      }
    }
    return null;
  }

  String? _findTopVisibleItemDateLabel() {
    // Sort items by index to check them in order
    final sortedEntries =
        _itemKeys.entries.toList()..sort((a, b) => a.key.compareTo(b.key));

    print('Checking ${sortedEntries.length} items for visibility');

    // Find the first visible news item at the top of the viewport
    for (final entry in sortedEntries) {
      final key = entry.value;
      final itemIndex = entry.key;

      if (key.currentContext != null) {
        final RenderBox? renderBox =
            key.currentContext!.findRenderObject() as RenderBox?;
        if (renderBox != null) {
          try {
            // Get the position of this item relative to the viewport
            final position = renderBox.localToGlobal(Offset.zero);

            // Get the scroll view's position
            final scrollContext =
                _scrollController.position.context.storageContext;
            if (scrollContext != null) {
              final scrollViewRenderBox =
                  scrollContext.findRenderObject() as RenderBox?;
              if (scrollViewRenderBox != null) {
                final scrollViewPosition = scrollViewRenderBox.localToGlobal(
                  Offset.zero,
                );
                final relativePosition = position.dy - scrollViewPosition.dy;

                print(
                  'Item $itemIndex: position=${position.dy}, scrollView=${scrollViewPosition.dy}, relative=$relativePosition',
                );

                // Check if this item is visible at the top
                // We use a more generous threshold to catch items at the top
                if (relativePosition >= -50 && relativePosition <= 150) {
                  final dateLabel = _itemDateLabels[itemIndex];
                  if (dateLabel != null) {
                    // ignore: prefer-number-format
                    print(
                      'Found top visible item at index $itemIndex with date: $dateLabel (relative pos: $relativePosition)',
                    );
                    return dateLabel;
                  }
                }
              }
            }
          } catch (e) {
            print('Error calculating position for item $itemIndex: $e');
          }
        }
      }
    }

    print('No visible item found, using fallback');
    // Fallback to first date label if no visible item found
    final newsBloc = context.read<NewsBloc>();
    final state = newsBloc.state;
    return _findFirstDateLabel(state.groupedNews);
  }

  String? _findLastDateLabel(List<Object?> groupedNews) {
    for (int i = groupedNews.length - 1; i >= 0; i--) {
      final item = groupedNews.elementAtOrNull(i);
      if (item is DayPart) {
        final l10n = EquitiLocalization.of(context);
        return DateFormatter.localizedDay(item, l10n);
      } else if (item is DateTime) {
        return intl.DateFormat('MMM d, yyyy').format(item);
      }
    }
    return null;
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final theme = context.duploTheme;
    final l10n = EquitiLocalization.of(context);
    final locale = Localizations.localeOf(context);

    return CustomScrollView(
      controller: _scrollController,
      slivers: [
        BlocBuilder<NewsBloc, NewsState>(
          buildWhen: (previous, current) => previous != current,
          builder: (blocBuilderContext, state) {
            // Initialize date label when news data is first loaded
            WidgetsBinding.instance.addPostFrameCallback((_) {
              _initializeDateLabel();
            });

            return switch (state.processState) {
              NewsLoading() => SliverFillRemaining(
                hasScrollBody: false,
                child: EventsNewsLoadingView(),
              ),
              NewsError() => SliverFillRemaining(
                hasScrollBody: false,
                child: EmptyOrErrorStateComponent.defaultError(
                  blocBuilderContext,
                  () {
                    blocBuilderContext.read<NewsBloc>().add(
                      NewsEvent.fetchNews(),
                    );
                  },
                ),
              ),
              _ => SliverMainAxisGroup(
                slivers: [
                  // if (!widget.isInMarketDetails)
                  //   SliverToBoxAdapter(
                  //     child: ColoredBox(
                  //       color: theme.background.bgSecondary,
                  //       child: Padding(
                  //         padding: const EdgeInsets.all(16),
                  //         child: DuploSearchInputField(
                  //           key: Key('symbols_search'),
                  //           onChanged: (_) {
                  //             debugPrint('onTextChanged');
                  //           },
                  //           controller: _textController,
                  //           hintText: l10n.trader_search,
                  //           isDisabled: true,
                  //           onTap:
                  //               () => showNewsSearchView(
                  //                 parentContext: context,
                  //                 textController: _textController,
                  //                 title: l10n.trader_search,
                  //               ),
                  //         ),
                  //       ),
                  //     ),
                  //   ),
                  SliverPersistentHeader(
                    pinned: true,
                    delegate: StickyDelegate(
                      child: Container(
                        color: theme.background.bgSecondary,
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 8,
                        ),
                        alignment:
                            intl.Bidi.isRtlLanguage(locale.languageCode)
                                ? Alignment.centerRight
                                : Alignment.centerLeft,
                        width: double.infinity,
                        child: DuploText(
                          textAlign: TextAlign.start,
                          text: state.dateLabel,
                          style: context.duploTextStyles.textMd,
                          fontWeight: DuploFontWeight.semiBold,
                          color: theme.text.textPrimary,
                        ),
                      ),
                    ),
                  ),

                  // News content
                  SliverPadding(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    sliver: PagedView.sliver(
                      padding: EdgeInsets.zero,
                      itemCount: state.groupedNews.length,
                      centerError: true,
                      centerLoading: true,
                      centerEmpty: true,
                      isLoading: switch (state.processState) {
                        NewsLoading() => true,
                        _ => false,
                      },
                      loadingBuilder:
                          (ctx) => Center(
                            child:
                                _textController.text.length < 1
                                    ? const SizedBox()
                                    : EventsNewsLoadingView(),
                          ),
                      emptyBuilder:
                          (ctx) => EmptyOrErrorStateComponent.empty(
                            title: l10n.trader_nothingToShow,
                            description: l10n.trader_noResultsDescription,
                            svgImage: trader.Assets.images.emptySearch.svg(
                              allowDrawingOutsideViewBox: true,
                            ),
                          ),
                      errorBuilder:
                          (ctx) => EmptyOrErrorStateComponent.error(
                            description:
                                l10n.trader_somethingWentWrongDescription,
                            title: l10n.trader_somethingWentWrong,
                            svgImage: trader.Assets.images.bug.svg(),
                            retryButtonText: l10n.trader_reload,
                            onTapRetry:
                                () => blocBuilderContext.read<NewsBloc>().add(
                                  NewsEvent.fetchNews(),
                                ),
                          ),
                      hasReachedMax: state.hasReachedMax,
                      separatorBuilder: (ctx, index) => SizedBox(height: 16),
                      onFetchData: () {
                        if (!state.hasReachedMax &&
                            state.processState !=
                                const NewsProcessState.loadingMore()) {
                          blocBuilderContext.read<NewsBloc>().add(
                            NewsEvent.fetchNews(loadMore: true),
                          );
                        }
                      },
                      itemBuilder: (BuildContext ctx, int index) {
                        final item = state.groupedNews[index];

                        if (item is DayPart || item is DateTime) {
                          // Update the current date label for subsequent news items
                          String itemDateLabel;
                          if (item is DayPart) {
                            itemDateLabel = DateFormatter.localizedDay(
                              item,
                              l10n,
                            );
                          } else {
                            itemDateLabel = intl.DateFormat(
                              'MMM d, yyyy',
                            ).format(item as DateTime);
                          }

                          // Store this date label for the next news items
                          _currentDateLabelForItems = itemDateLabel;

                          // Date headers are invisible but still take up space in the list
                          return const SizedBox.shrink();
                        } else if (item is NewsItem) {
                          // Create a unique key for this item
                          final key = _itemKeys.putIfAbsent(
                            index,
                            () => GlobalKey(),
                          );

                          // Store the current date label for this item
                          if (_currentDateLabelForItems != null) {
                            _itemDateLabels[index] = _currentDateLabelForItems!;
                          }

                          return Container(
                            key: key,
                            child: NewsListItem(newsItemDetails: item.item),
                          );
                        }
                        return const SizedBox.shrink();
                      },
                    ),
                  ),
                  !state.hasReachedMax
                      ? SliverPersistentHeader(
                        pinned: true,
                        floating: true,
                        delegate: StickyDelegate(
                          child: Center(
                            child: CircularProgressIndicator.adaptive(),
                          ),
                        ),
                      )
                      : SliverToBoxAdapter(),
                ],
              ),
            };
          },
        ),
      ],
    );
  }

  @override
  bool get wantKeepAlive => true;
}

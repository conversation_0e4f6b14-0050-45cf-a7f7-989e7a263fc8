import 'package:collection/collection.dart';
import 'package:duplo/duplo.dart';
import 'package:e_trader/src/assets/assets.gen.dart' as trader;
import 'package:e_trader/src/data/api/news_response_model.dart';
import 'package:e_trader/src/domain/formatter/date_formatter.dart';
import 'package:e_trader/src/presentation/discover/news/bloc/news_bloc.dart';
import 'package:e_trader/src/presentation/discover/news/widgets/events_news_loading_view.dart';
import 'package:e_trader/src/presentation/discover/news/widgets/news_list_item.dart';
import 'package:e_trader/src/presentation/duplo/sticky_delegate.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart' as intl;

class NewsTab extends StatefulWidget {
  const NewsTab({this.isInMarketDetails = false});
  final bool isInMarketDetails;

  @override
  State<NewsTab> createState() => _NewsTabState();
}

class _NewsTabState extends State<NewsTab> with AutomaticKeepAliveClientMixin {
  late final TextEditingController _textController;
  late final ScrollController _scrollController;
  String? _lastVisibleDateLabel;

  @override
  void initState() {
    super.initState();
    _textController = TextEditingController();
    _scrollController = ScrollController();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _textController.dispose();
    super.dispose();
  }

  void _onScroll() {
    final newsBloc = context.read<NewsBloc>();
    final state = newsBloc.state;

    // Find the date header that should be visible at the top
    final visibleDateLabel = _findVisibleDateLabel(state.groupedNews);

    // Only update if the label has actually changed and is different from current state
    if (visibleDateLabel != null &&
        visibleDateLabel != state.dateLabel &&
        visibleDateLabel != _lastVisibleDateLabel) {
      _lastVisibleDateLabel = visibleDateLabel;
      print('Updating date label to "$visibleDateLabel"');
      newsBloc.add(NewsEvent.updateDateLabel(dateLabel: visibleDateLabel));
    }
  }

  void _initializeDateLabel() {
    final newsBloc = context.read<NewsBloc>();
    final state = newsBloc.state;

    if (state.groupedNews.isNotEmpty && state.dateLabel.isEmpty) {
      // Find the first date header in the list
      final firstDateLabel = _findFirstDateLabel(state.groupedNews);
      if (firstDateLabel != null) {
        newsBloc.add(NewsEvent.updateDateLabel(dateLabel: firstDateLabel));
      }
    }
  }

  String? _findFirstDateLabel(List<Object?> groupedNews) {
    for (final item in groupedNews) {
      if (item is DayPart) {
        final l10n = EquitiLocalization.of(context);
        return DateFormatter.localizedDay(item, l10n);
      } else if (item is DateTime) {
        return intl.DateFormat('MMM d, yyyy').format(item);
      }
    }
    return null;
  }

  String? _findVisibleDateLabel(List<Object?> groupedNews) {
    if (groupedNews.isEmpty) return null;

    final scrollOffset = _scrollController.offset;

    // If we're at the very top, return the first date label
    if (scrollOffset <= 2) {
      return _findFirstDateLabel(groupedNews);
    }

    // Calculate cumulative heights with more accurate estimation
    double cumulativeHeight = 0;
    String? currentDateLabel;

    // More realistic height estimates based on typical news item layouts
    const double newsItemHeight = 140.0; // Increased estimate for news items
    const double dateHeaderHeight = 0.0; // Date headers are invisible
    const double sectionPadding = 16.0; // Additional padding between sections

    for (int i = 0; i < groupedNews.length; i++) {
      final item = groupedNews[i];

      if (item is DayPart) {
        // When we encounter a new date section, add some padding
        if (currentDateLabel != null) {
          cumulativeHeight += sectionPadding;
        }
        final l10n = EquitiLocalization.of(context);
        currentDateLabel = DateFormatter.localizedDay(item, l10n);
        cumulativeHeight += dateHeaderHeight;
      } else if (item is DateTime) {
        // When we encounter a new date section, add some padding
        if (currentDateLabel != null) {
          cumulativeHeight += sectionPadding;
        }
        currentDateLabel = intl.DateFormat('MMM d, yyyy').format(item);
        cumulativeHeight += dateHeaderHeight;
      } else if (item is NewsItem) {
        // Check if the current scroll position falls within this item's range
        final itemStartHeight = cumulativeHeight;
        final itemEndHeight = cumulativeHeight + newsItemHeight;

        // If the scroll position is within this item, or if this item extends
        // beyond the scroll position, then this is the visible section
        if (itemStartHeight <= scrollOffset && itemEndHeight > scrollOffset) {
          // print(
          //   'Found visible item at scroll $scrollOffset, item range: $itemStartHeight-$itemEndHeight, section: $currentDateLabel',
          // );
          return currentDateLabel ?? _findFirstDateLabel(groupedNews);
        }

        cumulativeHeight += newsItemHeight;
      }
    }

    // If we've scrolled past everything, return the last date label
    return _findLastDateLabel(groupedNews);
  }

  String? _findLastDateLabel(List<Object?> groupedNews) {
    for (int i = groupedNews.length - 1; i >= 0; i--) {
      final item = groupedNews.elementAtOrNull(i);
      if (item is DayPart) {
        final l10n = EquitiLocalization.of(context);
        return DateFormatter.localizedDay(item, l10n);
      } else if (item is DateTime) {
        return intl.DateFormat('MMM d, yyyy').format(item);
      }
    }
    return null;
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final theme = context.duploTheme;
    final l10n = EquitiLocalization.of(context);
    final locale = Localizations.localeOf(context);

    return CustomScrollView(
      controller: _scrollController,
      slivers: [
        BlocBuilder<NewsBloc, NewsState>(
          buildWhen: (previous, current) => previous != current,
          builder: (blocBuilderContext, state) {
            // Initialize date label when news data is first loaded
            WidgetsBinding.instance.addPostFrameCallback((_) {
              _initializeDateLabel();
            });

            return switch (state.processState) {
              NewsLoading() => SliverFillRemaining(
                hasScrollBody: false,
                child: EventsNewsLoadingView(),
              ),
              NewsError() => SliverFillRemaining(
                hasScrollBody: false,
                child: EmptyOrErrorStateComponent.defaultError(
                  blocBuilderContext,
                  () {
                    blocBuilderContext.read<NewsBloc>().add(
                      NewsEvent.fetchNews(),
                    );
                  },
                ),
              ),
              _ => SliverMainAxisGroup(
                slivers: [
                  // if (!widget.isInMarketDetails)
                  //   SliverToBoxAdapter(
                  //     child: ColoredBox(
                  //       color: theme.background.bgSecondary,
                  //       child: Padding(
                  //         padding: const EdgeInsets.all(16),
                  //         child: DuploSearchInputField(
                  //           key: Key('symbols_search'),
                  //           onChanged: (_) {
                  //             debugPrint('onTextChanged');
                  //           },
                  //           controller: _textController,
                  //           hintText: l10n.trader_search,
                  //           isDisabled: true,
                  //           onTap:
                  //               () => showNewsSearchView(
                  //                 parentContext: context,
                  //                 textController: _textController,
                  //                 title: l10n.trader_search,
                  //               ),
                  //         ),
                  //       ),
                  //     ),
                  //   ),
                  SliverPersistentHeader(
                    pinned: true,
                    delegate: StickyDelegate(
                      child: Container(
                        color: theme.background.bgSecondary,
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 8,
                        ),
                        alignment:
                            intl.Bidi.isRtlLanguage(locale.languageCode)
                                ? Alignment.centerRight
                                : Alignment.centerLeft,
                        width: double.infinity,
                        child: DuploText(
                          textAlign: TextAlign.start,
                          text: state.dateLabel,
                          style: context.duploTextStyles.textMd,
                          fontWeight: DuploFontWeight.semiBold,
                          color: theme.text.textPrimary,
                        ),
                      ),
                    ),
                  ),

                  // News content
                  SliverPadding(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    sliver: PagedView.sliver(
                      padding: EdgeInsets.zero,
                      itemCount: state.groupedNews.length,
                      centerError: true,
                      centerLoading: true,
                      centerEmpty: true,
                      isLoading: switch (state.processState) {
                        NewsLoading() => true,
                        _ => false,
                      },
                      loadingBuilder:
                          (ctx) => Center(
                            child:
                                _textController.text.length < 1
                                    ? const SizedBox()
                                    : EventsNewsLoadingView(),
                          ),
                      emptyBuilder:
                          (ctx) => EmptyOrErrorStateComponent.empty(
                            title: l10n.trader_nothingToShow,
                            description: l10n.trader_noResultsDescription,
                            svgImage: trader.Assets.images.emptySearch.svg(
                              allowDrawingOutsideViewBox: true,
                            ),
                          ),
                      errorBuilder:
                          (ctx) => EmptyOrErrorStateComponent.error(
                            description:
                                l10n.trader_somethingWentWrongDescription,
                            title: l10n.trader_somethingWentWrong,
                            svgImage: trader.Assets.images.bug.svg(),
                            retryButtonText: l10n.trader_reload,
                            onTapRetry:
                                () => blocBuilderContext.read<NewsBloc>().add(
                                  NewsEvent.fetchNews(),
                                ),
                          ),
                      hasReachedMax: state.hasReachedMax,
                      separatorBuilder: (ctx, index) => SizedBox(height: 16),
                      onFetchData: () {
                        if (!state.hasReachedMax &&
                            state.processState !=
                                const NewsProcessState.loadingMore()) {
                          blocBuilderContext.read<NewsBloc>().add(
                            NewsEvent.fetchNews(loadMore: true),
                          );
                        }
                      },
                      itemBuilder: (BuildContext ctx, int index) {
                        final item = state.groupedNews[index];
                        if (item is DayPart || item is DateTime) {
                          // Date headers are invisible but still take up space in the list
                          // The actual header display is handled by the sticky header above
                          return const SizedBox.shrink();
                        } else if (item is NewsItem) {
                          return NewsListItem(newsItemDetails: item.item);
                        }
                        return const SizedBox.shrink();
                      },
                    ),
                  ),
                  !state.hasReachedMax
                      ? SliverPersistentHeader(
                        pinned: true,
                        floating: true,
                        delegate: StickyDelegate(
                          child: Center(
                            child: CircularProgressIndicator.adaptive(),
                          ),
                        ),
                      )
                      : SliverToBoxAdapter(),
                ],
              ),
            };
          },
        ),
      ],
    );
  }

  @override
  bool get wantKeepAlive => true;
}

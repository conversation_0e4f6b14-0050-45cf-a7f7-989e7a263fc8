import 'dart:async';

import 'package:e_trader/src/domain/usecase/get_market_timing_calculator_use_case.dart';
import 'package:e_trader/src/domain/usecase/subscribe_to_symbol_quotes_use_case.dart';
import 'package:e_trader/src/presentation/market_hours/bloc/market_timing_calculator.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:monitoring/monitoring.dart';

part 'market_hours_bloc.freezed.dart';
part 'market_hours_event.dart';
part 'market_hours_state.dart';

class MarketHoursBloc extends Bloc<MarketHoursEvent, MarketHoursState> {
  final GetMarketTimingCalculatorUseCase _getMarketLimitCalculatorUseCase;
  final LoggerBase _logger;
  final SubscribeToSymbolQuotesUseCase _subscribeToSymbolQuotesUseCase;

  MarketHoursBloc({
    required LoggerBase logger,
    required GetMarketTimingCalculatorUseCase getMarketLimitCalculatorUseCase,
    required SubscribeToSymbolQuotesUseCase subscribeToSymbolQuotesUseCase,
  }) : _logger = logger,
       _getMarketLimitCalculatorUseCase = getMarketLimitCalculatorUseCase,
       _subscribeToSymbolQuotesUseCase = subscribeToSymbolQuotesUseCase,
       super(MarketHoursState()) {
    on<_GetMarketHours>(_getMarketHours);
  }

  FutureOr<void> _getMarketHours(
    _GetMarketHours event,
    Emitter<MarketHoursState> emit,
  ) async {
    final result =
        await _getMarketLimitCalculatorUseCase(
          symbolCode: event.symbolCode,
        ).run();

    await result.fold(
      (error) {
        addError(error);
        emit(state.copyWith(proccesState: MarketHoursProcessState.error()));
      },
      (checker) async {
        final symbolQuoteResult =
            await _subscribeToSymbolQuotesUseCase(
              symbol: event.symbolCode,
              subscriberId: '${MarketHoursBloc}_$hashCode',
            ).run();

        return symbolQuoteResult.fold(
          (left) {
            addError(left);
            emit(state.copyWith(proccesState: MarketHoursProcessState.error()));
          },
          (subscribeResultStream) {
            final marketOpenStream =
                subscribeResultStream
                    .map((q) => q.isMarketOpen)
                    .distinct(); // only forward changes

            // Emit initial success state immediately, then update when stream emits
            // This ensures the widget shows content even if stream hasn't emitted yet
            emit(
              state.copyWith(
                proccesState: MarketHoursProcessState.success(
                  checker,
                  null, // Will be updated when stream emits
                ),
              ),
            );

            return emit.forEach<bool?>(
              marketOpenStream,
              onData:
                  (isOpen) => state.copyWith(
                    proccesState: MarketHoursProcessState.success(
                      checker,
                      isOpen,
                    ),
                  ),
              onError: (error, stackTrace) {
                addError(error, stackTrace);
                return state.copyWith(
                  proccesState: MarketHoursProcessState.error(),
                );
              },
            );
          },
        );
      },
    );
  }

  @override
  void addError(Object error, [StackTrace? stackTrace]) {
    _logger.logError(error);
    super.addError(error, stackTrace);
  }
}

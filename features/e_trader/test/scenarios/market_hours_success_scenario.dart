import 'package:api_client/api_client.dart';
import 'package:clock/clock.dart';
import 'package:e_trader/fusion.dart';
import 'package:e_trader/src/di/di_container.dart';
import 'package:socket_client/socket_client.dart';

void senarioForSessionAndHoliday([
  String sessionFileName = "success",
  String holidayFileName = "success",
]) {
  diContainer.unregister<Clock>();
  diContainer.registerSingleton<Clock>(Clock.fixed(DateTime(2024, 1, 1)));
  diContainer<MockApiInterceptor>()
    ..reset()
    ..reply({
      'api/Symbol/get-holiday': [
        MockResponse(
          bodyFilePath: 'resources/mocks/holidays/$holidayFileName.json',
        ),
      ],
      'api/Symbol/get-session': [
        MockResponse(
          bodyFilePath: 'resources/mocks/sessions/$sessionFileName.json',
        ),
      ],
    });
  diContainer<MockSocketInterceptor>().addMockResponse(
    url: "https://equiti-backend-demo-dev.equiti.me.uk/hubs/productHub",
    eventType: TradingSocketEvent.quotes.subscribe,
    responses: [
      {
        "symbol": "EURCAD",
        "productName": "EURCAD",
        "ask": 100,
        "bid": 200,
        "date": "2024-11-22T05:15:03",
        "digits": 5,
        "spread": 2.3,
        "dailyRateChange": 0,
        "direction": "Up",
        "midPrice": 76.492,
        "isMarketOpen": false,
      },
    ],
    interval: Duration.zero,
  );
}

void marketHoursSuccessScenario() {
  senarioForSessionAndHoliday();
}

void marketHoursAllDayHolidayScenario() {
  senarioForSessionAndHoliday("success", "all_day_holiday");
}

void marketHoursComprehensiveScenario() {
  senarioForSessionAndHoliday("comprehensive_testing", "comprehensive_testing");
}

void marketHoursFullWeekHolidayScenario() {
  senarioForSessionAndHoliday("success", "full_week_holiday");
}

void marketHoursInvalidHolidayDateScenario() {
  senarioForSessionAndHoliday("success", "invalid_date_format");
}

void marketHoursMultipleHolidayDateScenario() {
  senarioForSessionAndHoliday("success", "multiple_day_holidays");
}

void marketHoursOverlappingDateScenario() {
  senarioForSessionAndHoliday(
    "overlaping_holidays_with_market",
    "overlaping_holidays_with_market",
  );
}

void marketHoursPartialDayHolidayScenario() {
  senarioForSessionAndHoliday("success", "partial_day_holiday");
}

void marketHoursInvalidTimingScenario() {
  senarioForSessionAndHoliday("invalid_market_session_timing", "success");
}

void marketHoursMarketDaysSkippedScenario() {
  senarioForSessionAndHoliday("market_days_skiped", "success");
}

void marketHoursSpanningMidnightScenario() {
  senarioForSessionAndHoliday("market_session_spanning_mignight", "success");
}

void marketHoursMultipleSessionSameDayScenario() {
  senarioForSessionAndHoliday("multiple_market_session_same_day", "success");
}

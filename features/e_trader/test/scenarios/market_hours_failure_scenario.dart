import 'package:api_client/api_client.dart';
import 'package:clock/clock.dart';
import 'package:e_trader/fusion.dart';
import 'package:e_trader/src/di/di_container.dart';
import 'package:socket_client/socket_client.dart';

void marketHoursFailureScenario() {
  diContainer.unregister<Clock>();
  diContainer.registerSingleton<Clock>(Clock.fixed(DateTime(2024, 12, 27)));
  diContainer<MockApiInterceptor>()
    ..reset()
    ..reply({
      'api/Symbol/get-holiday': [
        MockResponse(
          code: 400,
          bodyFilePath: 'resources/mocks/holidays/success.json',
        ),
      ],
      'api/Symbol/get-session': [
        MockResponse(
          code: 400,
          bodyFilePath: 'resources/mocks/sessions/failure.json',
        ),
      ],
    });
  diContainer<MockSocketInterceptor>().addMockResponse(
    url: "https://equiti-backend-demo-dev.equiti.me.uk/hubs/productHub",
    eventType: TradingSocketEvent.quotes.subscribe,
    responses: [
      {
        "symbol": "EURCAD",
        "productName": "EURCAD",
        "ask": 100,
        "bid": 200,
        "date": "2024-11-22T05:15:03",
        "digits": 5,
        "spread": 2.3,
        "dailyRateChange": 0,
        "direction": "Up",
        "midPrice": 76.492,
        "isMarketOpen": true,
      },
    ],
    interval: Duration.zero,
  );
}

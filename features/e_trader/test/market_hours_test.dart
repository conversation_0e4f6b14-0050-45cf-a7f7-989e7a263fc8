// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: unused_import, directives_ordering

import 'package:e_trader/src/presentation/market_hours/widgets/market_hour_details_widget_v2.dart';
import 'scenarios/market_hours_success_scenario.dart';
import 'scenarios/market_hours_failure_scenario.dart';
import 'package:equiti_test/equiti_test.dart';
import 'package:clock/clock.dart';

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import './bdd_hooks/hooks.dart';
import './step/the_app_is_rendered.dart';
import './step/i_wait_for_seconds.dart';
import './step/screenshot_verified_with_custom_pump.dart';

void main() {
  setUpAll(() async {
    await Hooks.beforeAll();
  });
  tearDownAll(() async {
    await Hooks.afterAll();
  });

  group('''Market Hours Golden Tests''', () {
    Future<void> beforeEach(String title, [List<String>? tags]) async {
      await Hooks.beforeEach(title, tags);
    }

    Future<void> afterEach(
      String title,
      bool success, [
      List<String>? tags,
    ]) async {
      await Hooks.afterEach(title, success, tags);
    }

    testGoldens(
      '''Outline: Market Hours Widget with Different Type of Data (scenarios: [marketHoursAllDayHolidayScenario], 'market_hours/all_day_holiday_scenario')''',
      (tester) async {
        var success = true;
        try {
          await beforeEach(
            '''Outline: Market Hours Widget with Different Type of Data (scenarios: [marketHoursAllDayHolidayScenario], 'market_hours/all_day_holiday_scenario')''',
          );
          await theAppIsRendered(
            tester,
            MarketHourDetailsWidgetV2(platformName: "EURCAD"),
            scenarios: [marketHoursAllDayHolidayScenario],
          );
          await iWaitForSeconds(tester, 1);
          await screenshotVerifiedWithCustomPump(
            tester,
            'market_hours/all_day_holiday_scenario',
          );
        } on TestFailure {
          success = false;
          rethrow;
        } finally {
          await afterEach(
            '''Outline: Market Hours Widget with Different Type of Data (scenarios: [marketHoursAllDayHolidayScenario], 'market_hours/all_day_holiday_scenario')''',
            success,
          );
        }
      },
    );
    testGoldens(
      '''Outline: Market Hours Widget with Different Type of Data (scenarios: [marketHoursComprehensiveScenario], 'market_hours/comprehensive_scenario')''',
      (tester) async {
        var success = true;
        try {
          await beforeEach(
            '''Outline: Market Hours Widget with Different Type of Data (scenarios: [marketHoursComprehensiveScenario], 'market_hours/comprehensive_scenario')''',
          );
          await theAppIsRendered(
            tester,
            MarketHourDetailsWidgetV2(platformName: "EURCAD"),
            scenarios: [marketHoursComprehensiveScenario],
          );
          await iWaitForSeconds(tester, 1);
          await screenshotVerifiedWithCustomPump(
            tester,
            'market_hours/comprehensive_scenario',
          );
        } on TestFailure {
          success = false;
          rethrow;
        } finally {
          await afterEach(
            '''Outline: Market Hours Widget with Different Type of Data (scenarios: [marketHoursComprehensiveScenario], 'market_hours/comprehensive_scenario')''',
            success,
          );
        }
      },
    );
    testGoldens(
      '''Outline: Market Hours Widget with Different Type of Data (scenarios: [marketHoursFullWeekHolidayScenario], 'market_hours/full_week_holiday_scenario')''',
      (tester) async {
        var success = true;
        try {
          await beforeEach(
            '''Outline: Market Hours Widget with Different Type of Data (scenarios: [marketHoursFullWeekHolidayScenario], 'market_hours/full_week_holiday_scenario')''',
          );
          await theAppIsRendered(
            tester,
            MarketHourDetailsWidgetV2(platformName: "EURCAD"),
            scenarios: [marketHoursFullWeekHolidayScenario],
          );
          await iWaitForSeconds(tester, 1);
          await screenshotVerifiedWithCustomPump(
            tester,
            'market_hours/full_week_holiday_scenario',
          );
        } on TestFailure {
          success = false;
          rethrow;
        } finally {
          await afterEach(
            '''Outline: Market Hours Widget with Different Type of Data (scenarios: [marketHoursFullWeekHolidayScenario], 'market_hours/full_week_holiday_scenario')''',
            success,
          );
        }
      },
    );
    testGoldens(
      '''Outline: Market Hours Widget with Different Type of Data (scenarios: [marketHoursInvalidHolidayDateScenario], 'market_hours/invalid_holiday_date_scenario')''',
      (tester) async {
        var success = true;
        try {
          await beforeEach(
            '''Outline: Market Hours Widget with Different Type of Data (scenarios: [marketHoursInvalidHolidayDateScenario], 'market_hours/invalid_holiday_date_scenario')''',
          );
          await theAppIsRendered(
            tester,
            MarketHourDetailsWidgetV2(platformName: "EURCAD"),
            scenarios: [marketHoursInvalidHolidayDateScenario],
          );
          await iWaitForSeconds(tester, 1);
          await screenshotVerifiedWithCustomPump(
            tester,
            'market_hours/invalid_holiday_date_scenario',
          );
        } on TestFailure {
          success = false;
          rethrow;
        } finally {
          await afterEach(
            '''Outline: Market Hours Widget with Different Type of Data (scenarios: [marketHoursInvalidHolidayDateScenario], 'market_hours/invalid_holiday_date_scenario')''',
            success,
          );
        }
      },
    );
    testGoldens(
      '''Outline: Market Hours Widget with Different Type of Data (scenarios: [marketHoursMultipleHolidayDateScenario], 'market_hours/multiple_holiday_date_scenario')''',
      (tester) async {
        var success = true;
        try {
          await beforeEach(
            '''Outline: Market Hours Widget with Different Type of Data (scenarios: [marketHoursMultipleHolidayDateScenario], 'market_hours/multiple_holiday_date_scenario')''',
          );
          await theAppIsRendered(
            tester,
            MarketHourDetailsWidgetV2(platformName: "EURCAD"),
            scenarios: [marketHoursMultipleHolidayDateScenario],
          );
          await iWaitForSeconds(tester, 1);
          await screenshotVerifiedWithCustomPump(
            tester,
            'market_hours/multiple_holiday_date_scenario',
          );
        } on TestFailure {
          success = false;
          rethrow;
        } finally {
          await afterEach(
            '''Outline: Market Hours Widget with Different Type of Data (scenarios: [marketHoursMultipleHolidayDateScenario], 'market_hours/multiple_holiday_date_scenario')''',
            success,
          );
        }
      },
    );
    testGoldens(
      '''Outline: Market Hours Widget with Different Type of Data (scenarios: [marketHoursOverlappingDateScenario], 'market_hours/overlapping_date_scenario')''',
      (tester) async {
        var success = true;
        try {
          await beforeEach(
            '''Outline: Market Hours Widget with Different Type of Data (scenarios: [marketHoursOverlappingDateScenario], 'market_hours/overlapping_date_scenario')''',
          );
          await theAppIsRendered(
            tester,
            MarketHourDetailsWidgetV2(platformName: "EURCAD"),
            scenarios: [marketHoursOverlappingDateScenario],
          );
          await iWaitForSeconds(tester, 1);
          await screenshotVerifiedWithCustomPump(
            tester,
            'market_hours/overlapping_date_scenario',
          );
        } on TestFailure {
          success = false;
          rethrow;
        } finally {
          await afterEach(
            '''Outline: Market Hours Widget with Different Type of Data (scenarios: [marketHoursOverlappingDateScenario], 'market_hours/overlapping_date_scenario')''',
            success,
          );
        }
      },
    );
    testGoldens(
      '''Outline: Market Hours Widget with Different Type of Data (scenarios: [marketHoursPartialDayHolidayScenario], 'market_hours/partial_day_holiday_scenario')''',
      (tester) async {
        var success = true;
        try {
          await beforeEach(
            '''Outline: Market Hours Widget with Different Type of Data (scenarios: [marketHoursPartialDayHolidayScenario], 'market_hours/partial_day_holiday_scenario')''',
          );
          await theAppIsRendered(
            tester,
            MarketHourDetailsWidgetV2(platformName: "EURCAD"),
            scenarios: [marketHoursPartialDayHolidayScenario],
          );
          await iWaitForSeconds(tester, 1);
          await screenshotVerifiedWithCustomPump(
            tester,
            'market_hours/partial_day_holiday_scenario',
          );
        } on TestFailure {
          success = false;
          rethrow;
        } finally {
          await afterEach(
            '''Outline: Market Hours Widget with Different Type of Data (scenarios: [marketHoursPartialDayHolidayScenario], 'market_hours/partial_day_holiday_scenario')''',
            success,
          );
        }
      },
    );
    testGoldens(
      '''Outline: Market Hours Widget with Different Type of Data (scenarios: [marketHoursInvalidTimingScenario], 'market_hours/invalid_timing_scenario')''',
      (tester) async {
        var success = true;
        try {
          await beforeEach(
            '''Outline: Market Hours Widget with Different Type of Data (scenarios: [marketHoursInvalidTimingScenario], 'market_hours/invalid_timing_scenario')''',
          );
          await theAppIsRendered(
            tester,
            MarketHourDetailsWidgetV2(platformName: "EURCAD"),
            scenarios: [marketHoursInvalidTimingScenario],
          );
          await iWaitForSeconds(tester, 1);
          await screenshotVerifiedWithCustomPump(
            tester,
            'market_hours/invalid_timing_scenario',
          );
        } on TestFailure {
          success = false;
          rethrow;
        } finally {
          await afterEach(
            '''Outline: Market Hours Widget with Different Type of Data (scenarios: [marketHoursInvalidTimingScenario], 'market_hours/invalid_timing_scenario')''',
            success,
          );
        }
      },
    );
    testGoldens(
      '''Outline: Market Hours Widget with Different Type of Data (scenarios: [marketHoursMarketDaysSkippedScenario], 'market_hours/market_days_skipped_scenario')''',
      (tester) async {
        var success = true;
        try {
          await beforeEach(
            '''Outline: Market Hours Widget with Different Type of Data (scenarios: [marketHoursMarketDaysSkippedScenario], 'market_hours/market_days_skipped_scenario')''',
          );
          await theAppIsRendered(
            tester,
            MarketHourDetailsWidgetV2(platformName: "EURCAD"),
            scenarios: [marketHoursMarketDaysSkippedScenario],
          );
          await iWaitForSeconds(tester, 1);
          await screenshotVerifiedWithCustomPump(
            tester,
            'market_hours/market_days_skipped_scenario',
          );
        } on TestFailure {
          success = false;
          rethrow;
        } finally {
          await afterEach(
            '''Outline: Market Hours Widget with Different Type of Data (scenarios: [marketHoursMarketDaysSkippedScenario], 'market_hours/market_days_skipped_scenario')''',
            success,
          );
        }
      },
    );
    testGoldens(
      '''Outline: Market Hours Widget with Different Type of Data (scenarios: [marketHoursSpanningMidnightScenario], 'market_hours/spanning_midnight_scenario')''',
      (tester) async {
        var success = true;
        try {
          await beforeEach(
            '''Outline: Market Hours Widget with Different Type of Data (scenarios: [marketHoursSpanningMidnightScenario], 'market_hours/spanning_midnight_scenario')''',
          );
          await theAppIsRendered(
            tester,
            MarketHourDetailsWidgetV2(platformName: "EURCAD"),
            scenarios: [marketHoursSpanningMidnightScenario],
          );
          await iWaitForSeconds(tester, 1);
          await screenshotVerifiedWithCustomPump(
            tester,
            'market_hours/spanning_midnight_scenario',
          );
        } on TestFailure {
          success = false;
          rethrow;
        } finally {
          await afterEach(
            '''Outline: Market Hours Widget with Different Type of Data (scenarios: [marketHoursSpanningMidnightScenario], 'market_hours/spanning_midnight_scenario')''',
            success,
          );
        }
      },
    );
    testGoldens(
      '''Outline: Market Hours Widget with Different Type of Data (scenarios: [marketHoursMultipleSessionSameDayScenario], 'market_hours/multiple_session_same_day_scenario')''',
      (tester) async {
        var success = true;
        try {
          await beforeEach(
            '''Outline: Market Hours Widget with Different Type of Data (scenarios: [marketHoursMultipleSessionSameDayScenario], 'market_hours/multiple_session_same_day_scenario')''',
          );
          await theAppIsRendered(
            tester,
            MarketHourDetailsWidgetV2(platformName: "EURCAD"),
            scenarios: [marketHoursMultipleSessionSameDayScenario],
          );
          await iWaitForSeconds(tester, 1);
          await screenshotVerifiedWithCustomPump(
            tester,
            'market_hours/multiple_session_same_day_scenario',
          );
        } on TestFailure {
          success = false;
          rethrow;
        } finally {
          await afterEach(
            '''Outline: Market Hours Widget with Different Type of Data (scenarios: [marketHoursMultipleSessionSameDayScenario], 'market_hours/multiple_session_same_day_scenario')''',
            success,
          );
        }
      },
    );
  });
}
